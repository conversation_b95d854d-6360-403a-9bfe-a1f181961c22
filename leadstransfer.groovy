/*
 * <AUTHOR>
 * @codeName 【商机】新建/编辑前验证
 * @description 新建编辑前验证一些规则，不合规则阻塞
 * @createTime 2024-04-07
 */

log.info(Fx.json.toJson(context))
String apiName = context.apiName as String
log.info("apiName=${apiName}")
Map finalResult = ["error": false, "errorMessage": ""]
String sjid = context.data._id as String
String ywlx1 = context.data.record_type as String
String sales_stage = context.data.sales_stage
String life_status = context.data.life_status as String
String sales_status = context.data.sales_status as String


//是否有生免流水线校验
Map checkResult1 = checkShengMianFlow(context.data)
if (checkResult1 != null) {
    //直接返回异常!
    return checkResult1
}

//竞品产品
Map checkResult2 = checkCompetitor(context.data)
if (checkResult2 != null) {
    //直接返回异常!
    return checkResult2
}


//合作共建商机
Map checkResult3 = checkApproval(context.data)
if (checkResult3 != null) {
    //直接返回异常!
    return checkResult3
}

//是否参与招标
Map checkResult4 = checkInvitation(context.data)
if (checkResult4 != null) {
    //直接返回异常!
    return checkResult4
}




//订单执行阶段提醒  2025-01-20 取消
/*Map checkResult6 = checkOrderFulfillment(context.data)
if (checkResult6 != null) {
    finalResult = checkResult6
}*/

//检测试剂商机产品明细合理性
List<Map> productList = context.details.object_uB9I1__c as List<Map>
// log.info("productList=${productList}")
Map checkResult7 = checkSjOppoProductDetail(context.data,productList)
if (checkResult7 != null) {
    finalResult = checkResult7
    if(finalResult.error!=null && finalResult.error){
        return finalResult
    }
}

if(ywlx1=='record_HRof3__c'){
  Map checkResult8 = checkXslc(context.data)
  if (checkResult8 != null) {
    finalResult = checkResult8
    if(finalResult.error!=null && finalResult.error){
        return finalResult
    }
  }
}

log.info("校验Autolas B-1 Star流水线相关产品")
if(ywlx1=='default__c' && life_status!='ineffective'){
  String targetProd = context.data.field_7Y19e__c as String //意向产品
  log.info("targetProd=${targetProd};sales_stage=${sales_stage};life_status=${life_status}")
  if(targetProd!=null && targetProd=='51' && sales_stage=='h41ehj5Xt'){
    List<Map> proList = context.details['NewOpportunityLinesObj'] as List<Map>
    // log.info("proList=${proList}")
    if(!proList){
      return ["error": true, "errorMessage": "当意向产品选择Autolas B-1 Star时，商机产品明细必填", "block": true]
    }
  }
}

if(apiName=='Add_button_default' && sales_status){
  log.info("商机新建时增加未转化线索的校验")
  Map checkResult5 = checkLeads(context.data)
  if (checkResult5 != null) {
      //直接返回异常!
      return checkResult5
  }
  //校验线索转化来的商机，是否之前存在相同商机
  Map checkResult6 = checkOppoAlreadyCreated(context.data)
   if (checkResult6 != null) {
      //直接返回异常!
      return checkResult6
  }
  
}else if(apiName=='Add_button_default' && !sales_status){
  log.info("线索转换商机场景跳过未转化线索的校验")
}else{
  log.info("编辑时跳过未转化线索的校验")
}



return finalResult

////////////////////////////////////////////////主函数结束///////////////////////////////////////////////

/**
 * 流水线/非流水线商机推进到订单执行阶段时，触发提醒，获取商机产品明细中，产品-项目大类字段不为空的产品名称加数量，
 * 提醒：赢单XXX（产品）5个，XXX（产品）3个，请确认本次赢单数量是否正确，如需调整请进入商机产品明细修改，点击继续保存将触发省区经理审批。
 */
Map checkOrderFulfillment(Map mainData){
    String sales_stage = mainData.sales_stage
    String record_type = mainData.record_type
    String sjId = mainData._id
    if((sales_stage=='4' ) && (record_type =='default__c' || record_type == 'record_HRof3__c')){
        String msg = "提醒："
        Map childObj = context.details as Map
        List<Map> productList = queryProductDetail(sjId)
        log.info("产品数量=${productList!=null?productList.size():0}")
        if(productList){
            msg+="赢单"
            productList.each{
                Map item ->
                    String productId = item.product_id
                    Map product = queryProduct(productId)
                    String productName = product.name
                    String quantity = item.quantity as String
                    msg+="${productName}${quantity}个，"
            }
        }
        msg += "请确认本次赢单数量是否正确，如需调整请进入商机产品明细修改，点击继续保存将触发省区经理审批"
        return ["error": true, "errorMessage": msg,"block":false]
    }
}

/**
 * 是否有生免流水线校验
 * @param mainData
 * @return
 */
Map checkShengMianFlow(Map mainData) {
    String ywlx = mainData.record_type as String //合作共建：record_r99oO__c；流水线商机：default__c
    if (ywlx == 'default__c') {
        //2024-04-22陈文浩新增：现在科室是否有生免流水线=是时，必须创建生免流水线品牌从对象，否则阻断
        String sf1 = mainData.field_xRg5C__c as String
        List mx = context.details.object_7bK9o__c as List
        BigDecimal mxsl = 0
        if (mx != null && mx != []) {
            mxsl = mx.size()
        }
        String sjjd1 = mainData.sales_stage as String
        log.info("是否有生免流水线：" + sf1)
        log.info("1为是，2为否")
        log.info("明细：" + mx)
        if (sf1 != null && sf1 == "1" && mxsl == 0 && sjjd1 =="h41ehj5Xt") {
            return ["error": true, "errorMessage": "当字段【是否有生免流水线】=是时，必须创建从对象【竞品产品信息】", "block": true]
        }
    }
    return null;
}


/**
 * 2024-05-07陈文浩新增：基于多选字段【现在科室包含竞品】的选项，例如该字段选择
 * 免疫设备,生化设备,生免流水线，则至少要有3条明细才可以提交。
 * @return
 */
Map checkCompetitor(Map mainData) {

    String sjjd = mainData.sales_stage as String //商机关联：1
    String sfdycp = mainData.field_oC31t__c as String //是否调研产品
    String ywlx = mainData.record_type as String
    if (ywlx == "record_r99oO__c" && sjjd == "1" && sfdycp == "1") {
        List bhjp = mainData.field_x2O78__c as List //现在科室包含竞品
        BigDecimal jpsl = (bhjp == null ? 0 : bhjp.size() as BigDecimal) //竞品数量
        List jpcpxx = context.details.object_7bK9o__c as List //竞品产品信息
        BigDecimal jpmxsl = (jpcpxx == null ? 0 : jpcpxx.size() as BigDecimal) //竞品产品信息明细数量
        if (jpmxsl == 0) {
            return ["error": true, "errorMessage": "请在“商机关联”阶段任务中选择“现在科室包含竞品”后，至少填写一条竞品产品信息明细", "block": true]
        }
        if (jpsl > 0 && jpsl > jpmxsl) {
            return ["error": true, "errorMessage": "竞品产品信息明细条数最少为" + jpsl + "条", "block": true]
        }
    }
    return null;
}

/**
 * 2024-05-07陈文浩新增：合作共建商机类型，如果有进行中的审批则不允许编辑数据
 * @param mainData
 * @return
 */
Map checkApproval(Map mainData) {
    String ywlx = mainData.record_type as String
    String id1 = mainData._id as String
    if (ywlx == "record_r99oO__c") {
        def retData = Fx.approval.findInstances("NewOpportunityObj", ["in_progress"], id1, 10, 0).result() as QueryResult
        if (retData.size > 0) {
            return ["error": true, "errorMessage": "有进行中的审批，不允许编辑", "block": true]
        }
    }
    return null;
}


/**
 * 2024-05-20陈文浩新增：合作共建商机类型，未填写项目收益测算时，不允许填写字段是否参与招标
 * @param mainData
 * @return
 */
Map checkInvitation(Map mainData) {
    String sfcyzb = mainData.field_5S58i__c as String //是否参与招标
    BigDecimal sbtrys = mainData.field_Eg81R__c as BigDecimal //设备投入预算金额（万元）
    BigDecimal rjtrysje = mainData.field_ldic8__c as BigDecimal //软件投入预算金额（万元）
    BigDecimal xmbzj = mainData.field_kP201__c as BigDecimal //项目保证金（万元）
    BigDecimal zxtrys = mainData.field_C48df__c as BigDecimal //装修投入预算金额（万元）
    BigDecimal yyhkzq = mainData.field_mpFja__c as BigDecimal //医院回款账期（月）
    BigDecimal zcpcge = mainData.field_fs725__c as BigDecimal //自产品采购额
    BigDecimal wccpje = mainData.field_BojAF__c as BigDecimal //外采产品金额
    BigDecimal jykncg = mainData.field_23CAw__c as BigDecimal //检验科年采购
    String sfclzsq = mainData.is_exceed_president_author__c as String // 是否超刘总授权
    if ( wccpje == null || jykncg == null || zxtrys == null || yyhkzq == null || zcpcge == null || sfclzsq==null || sbtrys == null || rjtrysje == null || xmbzj == null) {
        if (sfcyzb != null && sfcyzb != "") {
            return ["error": true, "errorMessage": "填写【是否参与招标】会触发投标审批，提醒杨小刚总监、李强总、刘军总审批，请确认是否项目已经需要招标，如需要请先联系杨薇薇维护项目收益测算信息，否则无法填写【是否参与招标】", "block": true]
        }
    }
    return null;
}



/**
 * 查询产品名称
 */
Map queryProduct(String id){
    Map ret = Fx.object.findOne("ProductObj",
            FQLAttribute.builder()
                    .columns(["_id", "name"])
                    .queryTemplate( QueryTemplate.AND(["_id": QueryOperator.EQ(id)] ))
                    .build(),
            SelectAttribute.builder()
                    .build()).result() as Map;
    return ret
}

/**
 * 查询商机产品明细
 */
List<Map> queryProductDetail(String sjId){
    List<Map> result = []
    //排除系列：带动试剂系列
    String sql = "select _id, name,quantity,product_id from NewOpportunityLinesObj where new_opportunity_id = '${sjId}' and field_gVAX3__c !='659cfd5d8c4fa700013a1b3c' ";
    Fx.object.select(sql, SelectAttribute.builder().build(), { list->result.addAll(list);
    }).result();
    return result
}


/**
 * 检测试剂商机产品明细合理性
 */
Map checkSjOppoProductDetail(Map mainData,List<Map> detailList){
    log.info("检测试剂商机产品明细合理性判断")
    // log.debug(mainData)
    // log.debug(detailList)
    Map result = null
    String record_type = mainData.record_type
    String sjId = mainData._id
    String customerId = mainData.account_id as String
    boolean isMobileOrgCreated = false// 是否为移动端机构用户创建
    String describe = mainData.field_2jpGe__c as String //商机来源描述
    String creator_id = mainData.field_mini_program_creator_id__c as String //小程序创建人ID
    String agent_code = mainData.field_agent_code__c as String //代理商编码


    if(record_type =='record_imdRU__c' && detailList!=null && detailList.size()>0){
        if(describe && creator_id && !agent_code){
            isMobileOrgCreated = true
        }
        if(isMobileOrgCreated){
            log.info("为移动端机构用户创建,跳过验证")
            return null
        }
        Map commonAccount = AccountUtils.queryCommonAccForSj()
        String commonAcId = commonAccount._id as String
        log.info("customerId=${customerId},commonAcId=${commonAcId}")
        if(customerId == commonAcId){
            result = ["error": true, "errorMessage": "当前客户为试剂商机公用终端，请完成终端替换","block":false]
        }else{
            String commonAcName = AccountUtils.queryCommonAccNameForSj()
            String msg = ""
            List<Map> commonAcProductList = []
            detailList.each{
                Map item ->
                    //判断对应的来源池的终端名称是否为公共终端
                    String accountName = item.field_source_pool_device_name__c //来源池终端名称,前验证函数里边是名称，后动作和其他场景是id
                    log.info("accountName=${accountName}")
                    if(accountName == commonAcName || accountName == commonAcId){
                        commonAcProductList.add(item)
                    }
            }
            if(commonAcProductList.size()>0){
                String accountId = mainData.account_id as String
                Map account = AccountUtils.queryAccountById(accountId)
                Map resultData = ReagentOppoUtils.judgeCommonAccountSourcePoolData(commonAcProductList,account,mainData)
                String errorMsg = resultData.errorMsg as String
                boolean checkResult = resultData.checkResult as boolean

                msg = errorMsg
                boolean errorData = false
                boolean blockData = false
                if(!checkResult){
                    errorData = true
                    blockData = true
                    result = ["error": errorData, "errorMessage": msg,"block":blockData]
                }else{
                    //进行终端的修改
                    //放在后动作中修改,此处修改不生效

                }

            }
        }

    }

    return result
}

/**
 * 检测商机意向渠道评估明细是否有重复
 */
Map checkSjQuDaoPingGuDetail(Map mainData,List<Map> detailList){
    String msg = ""
    boolean errorData = false
    boolean blockData = false
    Map result = null
    Map<String,Map> qudaoMap = [:]
    List<String> repeatNameIdList = []
    detailList.each {
        Map item ->
            String qudao = item["field_competitor_agents__c"] as String
            if(qudaoMap.containsKey(qudao)){
                errorData = true
                msg = "商机意向渠道评估明细中：xxxx重复，请删除重复数据!"
                blockData = true
                repeatNameIdList.add(qudao)
            }else{
                qudaoMap.put(qudao, item)
            }
    }
    if(repeatNameIdList){
        QueryResult ret = Fx.object.find("object_oK61V__c",
                FQLAttribute.builder()
                        .columns(["_id", "name"])
                        .queryTemplate(QueryTemplate.AND(["_id": QueryOperator.IN(repeatNameIdList)]))
                        .limit(100)
                        .build(),
                SelectAttribute.builder().build()).result() as QueryResult
        List<Map> repeatDataList = ret.dataList as List<Map>
        List<String> repeatNameList = repeatDataList.collect{ x -> x.name as String}
        msg = msg.replace("xxxx", repeatNameList.join(","))
    }

    if(errorData){
        result = ["error": errorData, "errorMessage": msg,"block":blockData]
    }

    return result
}

/**
 * 检查非流水线商机销售流程
 */
Map checkXslc(Map mainData){
    DateTime createTime = mainData.create_time as DateTime
    String b = "2025-01-24 00:00"
    DateTime dateTime = DateTime.of(b)
    log.info("创建时间：${createTime},指定时间：${dateTime}")
    if(createTime !=null && createTime.toTimestamp() <= dateTime.toTimestamp() ){
      //只对指定时间之后的数据生效
      return null
    }
    
    String salesProcessId = mainData.sales_process_id as String //销售流程
    String name = mainData.name as String
    String targetDevice = mainData.field_y2T41__c as String //意向仪器
    String yycp = mainData.field_7Y19e__c as String //意向产品
    String khsf = mainData.field_gKwwU__c as String //客户省份
    log.info("当前主数据为${name},意向仪器为：${targetDevice}")
    if(!targetDevice){
     //标讯通新建漏单商机没有意向仪器，此处放行
      if(khsf!='河南省'&&(yycp=='15'||yycp=='16') && salesProcessId != "64a264d714b086000178d554"){
        return ["error": true, "errorMessage": "销售流程选择错误，请检查！", "block": true]
      }
     return null
    }
    List<Map<String,String>> dicMapList = DicUtils.queryDicByNameLike("商机非重点仪器ID") //仪器和销售流程关系
    Map<String,String> dicMap = [:]
    dicMapList.each{
      Map<String,String> item ->
      List<String> keys = item.keys()
      keys.each{
        String k ->
        String value = item[k] as String
        String[] ks = k.split(";")
        ks.each{
          String k1 ->
          dicMap.put(k1,value)
        }
      }
    }
    log.info("当前字典数据为：${dicMap}")
    String targetProcessId
    if(targetDevice){
     String key = targetDevice as String
     log.info("当前意向仪器：${key}")
     String value = dicMap[key] as String
     if(value){
        targetProcessId = value
        log.info("为指定销售流程！")
     }else{
        targetProcessId = "64a264d714b086000178d554"
        log.info("为非流水线销售流程！")
     }
    }
   if(targetProcessId!=salesProcessId){
     return ["error": true, "errorMessage": "销售流程选择错误，请检查！", "block": true]
   }else{
     return null
   }
}

/**
 * 根据客户+意向产品+意向仪器查询未转化的线索，存在则不允许创建商机
 */
Map checkLeads(Map mainData){
  String source_lead_name__c = mainData.source_lead_name__c as String //源线索编号
  if(!source_lead_name__c){
    source_lead_name__c = '-****************'
  }
  //当前时间月份减1
  Date now = Date.now()
  Date lastMonth = now - 1.months
  Long lastMonthTimestamp = lastMonth.toTimestamp()
  log.info("当前时间：" + now+";上个月时间：" + lastMonth+";时间戳=${lastMonth.toTimestamp()}")
  
  String targetProd = mainData.field_7Y19e__c as String //意向产品
  String targetDevice = mainData.field_y2T41__c as String //意向仪器
  String customerId = mainData.account_id as String //客户id
  Map searchAccount = Fx.object.findOne("object_8I130__c", 
             FQLAttribute.builder()
                .columns(["_id", "name"]) 
                .queryTemplate( QueryTemplate.AND(["field_pnf0b__c": QueryOperator.EQ(customerId)] ))
                .build(),
             SelectAttribute.builder()
                .build()).result() as Map;
  if(searchAccount){
    String accId = searchAccount._id as String
    log.info("当前客户对应的客户查询为：${searchAccount}")
    def sql = "select _id, field_rzv5M__c, name,last_modified_time,invalid_time__c,biz_status from LeadsObj where 1=1 and name !='${source_lead_name__c}' and field_3Qcib__c = '${accId}' and "
    sql += "( biz_status ='un_processed' or biz_status ='processed' or biz_status ='closed' )";
    
    if(targetProd){
      sql += " and field_Tu38N__c = '${targetProd}' "
    }else{
      sql += " and field_Tu38N__c is null "
    }
    
    if(targetDevice){
      sql += " and field_uVOwy__c = '${targetDevice}' "
    }else{
      sql += " and field_uVOwy__c is null "
    }
    log.info("sql2=${sql}")
    SelectAttribute att = SelectAttribute.builder()
      .needInvalid(false)
      .build()
    QueryResult leadsRst = Fx.object.select(sql, att).result() as QueryResult
    List<Map> searchLeadsList = []
    List<Map> lls = leadsRst.dataList as List<Map>
    lls.each{
      Map item ->
      String biz_status = item.biz_status as String
      log.info("明细数据为：${item}")
      if(biz_status=='closed'){
        //无效商机必须是1个月前的才算真无效不干扰商机创建
        String lastModifyTime = item.last_modified_time as String
        long lastModifyTimeStamp = DateTime.of(lastModifyTime).toTimestamp()
        String invalidTime = item.invalid_time__c as String
        long invalidTimeStamp = invalidTime?DateTime.of(invalidTime).toTimestamp():0
        log.info("lastModifyTimeStamp=${lastModifyTimeStamp};invalidTimeStamp=${invalidTimeStamp}")
        boolean notOk = false
        if(invalidTimeStamp==0){
          if(lastModifyTimeStamp > lastMonthTimestamp){
            notOk = true
          }
        }else{
          if(invalidTimeStamp > lastMonthTimestamp){
            notOk = true
          }
        }
        if(notOk){
          searchLeadsList.add(item)
        }
      }else{
        searchLeadsList.add(item)
      }
      
      
    }
    if(searchLeadsList){
        List<String> nameList = searchLeadsList['name']
        String names = nameList.join(",")
        return ["error": true, "errorMessage": "当前客户，相同产品及仪器存在未转化的销售线索，不允许新建商机，需要进行转化处理，无效线索仍可转换。相关线索为："+names, "block": true]
    }
  }

  return null;
}

/**
 * 校验线索转化来的商机，是否之前存在相同商机
 */
Map checkOppoAlreadyCreated(Map mainData){
  String source_lead_name__c = mainData.source_lead_name__c as String //源线索编号
  if(source_lead_name__c){
    log.info("线索转化商机校验：")
    List<String> oppoStage = ["5","e621l33Ai","6"] //商机阶段不等于订单执行、启用管理、丢单的为跟进中商机
    String oppoStages = "'"+oppoStage.join("','")+"'"
    String accountId = mainData.account_id as String
    String targetProd = mainData.field_7Y19e__c as String //意向产品
    String targetDevice = mainData.field_y2T41__c as String //意向仪器
    String sql = "select _id,name,owner from NewOpportunityObj where sales_stage not in (${oppoStages}) and account_id = '${accountId}' "
    if(targetProd){
      sql += " and field_7Y19e__c = '${targetProd}' "
    }else{
      sql += " and field_7Y19e__c is null "
    }
    if(targetDevice){
      sql += " and field_y2T41__c = '${targetDevice}' "
    }else{
      sql += " and field_y2T41__c is null "
    }
    sql +=" order by last_modified_time desc limit 100 offset 0 "
    QueryResult rst = Fx.object.select(sql).result() as QueryResult
    List<Map> dataList = rst.dataList
    if(dataList){
      Map oppo = dataList.get(0) //存在多个取最后修改时间最近的
      String oppoName = oppo.name as String
      String sjId = oppo._id as String
      List<String> ownerList = (oppo.owner as List<String>)
      String ownerId = ownerList?ownerList.get(0):null
      log.info("存在相同的商机，当前线索不再创建新商机，直接转换！相关商机为：${dataList}")

      Map leadsData = Fx.object.findOne("LeadsObj",
           FQLAttribute.builder()
              .columns(["_id","name","sales_lead__c","field_ep5se__c"])
              .queryTemplate( QueryTemplate.AND(["name": QueryOperator.EQ(source_lead_name__c)] ))
              .build(),
           SelectAttribute.builder()
              .build()).result() as Map;
      log.info("使用源线索名称查询结果：${leadsData}")
      
      //转线索
      String leadsId = leadsData._id as String
      String leadsName = leadsData.name as String
      Map account = [
        "object_describe_api_name": "AccountObj",
        "_id": accountId
      ]
      Map contact = [:]
      Map newOpportunity = ["_id": sjId]
      Map newObjectData = ["object_data": newOpportunity]
      Map opportunity = [:]
      log.info("线索转商机参数：leadsId=${leadsId}；account=${account}；newObjectData=${newObjectData}")
      def(boolean error, Object data, String msg) = Fx.crm.leads.transfer(true, true, leadsId, account, contact, newObjectData, opportunity)
      if (error) {
        log.info("线索转换失败：" + msg)
        log.info("准备回退线索状态!")
        revertLeads(leadsId)
        return ["error": true, "errorMessage": "线索转换失败，请稍后再试！", "block": true]
      } else {
        log.info("线索转换成功！")
      }
      
      //查渠道
      List<String> quDaoIdList = []
      String leadsSales = leadsData["sales_lead__c"] as String //所属渠道
      if(leadsSales){
        quDaoIdList.add(leadsSales)
      }
      
      String qdName = leadsData["field_ep5se__c"] as String //渠道名称
      if(qdName){
        def qudao = Fx.object.findOne("object_oK61V__c", 
                 FQLAttribute.builder()
                    .columns(["_id", "name"]) 
                    .queryTemplate( QueryTemplate.AND(["name": QueryOperator.EQ(qdName)] ))
                    .build(),
                 SelectAttribute.builder()
                    .build()).result() as Map;
        log.info("根据渠道名称${qdName}查询渠道：${qudao}")
        if(qudao){
          quDaoIdList.add(qudao._id as String)
        }
      }
      
      quDaoIdList = Fx.utils.listUnique(quDaoIdList)
      log.info("相关渠道id为：${quDaoIdList}")
      if(quDaoIdList){
        //和现有商机渠道做排重
        def qdRet = Fx.object.find("object_sTnhW__c", 
             FQLAttribute.builder()
                .columns(["_id", "name","field_competitor_agents__c"])
                .queryTemplate(QueryTemplate.AND(["field_opportunity_id__c": QueryOperator.EQ(sjId)]))
                .build(),
             SelectAttribute.builder().build()).result() as QueryResult
        log.info("现存意向渠道信息为：${qdRet}")
        List<String> hadQdList = qdRet.dataList['field_competitor_agents__c']
        List<String> needAddQuDaoList = []
        quDaoIdList.each{
          String qdId ->
          if(!hadQdList.contains(qdId)){
            needAddQuDaoList.add(qdId)
          }
        }
        if(needAddQuDaoList){
          needAddQuDaoList.each{
            String qdId ->
            Map masterData = [
              "field_competitor_agents__c": qdId,  
              "owner" : [ownerId], 
              "field_opportunity_id__c": sjId
            ]
            def ret = Fx.object.create("object_sTnhW__c", masterData, null, CreateAttribute.builder().build())
            if(ret[0]){
              log.info("意向渠道创建失败！${ret[2]}")
            }else{
              log.info("意向渠道创建成功！")
            }
          }
        }else{
          log.info("商机中意向渠道已存在，将当前商机的源线索改为当前线索:${leadsName}")
          String objectAPIName = 'NewOpportunityObj'
          Map updateData = [
            "leads_id":leadsId,
            "source_lead_name__c":leadsName
          ]
          def (Boolean error2, Map data2, String errorMessage2) =  Fx.object.update(objectAPIName, sjId, updateData, UpdateAttribute.builder().triggerWorkflow(true).build())
          if (error2) {
            log.info("商机源线索修改失败:" + errorMessage2 )
          }else{
            log.info("商机源线索修改成功！"  )
          }
          
        }
        log.info("渠道维护完毕！")
      }else{
        log.info("暂无渠道信息")
      }
      
      return ["error": true, "errorMessage": "存在跟进中商机"+oppoName+"，当前线索将自动关联商机，请关闭当前界面刷新线索后查看！", "block": true]
    }else{
      log.info("不存在相同商机！")
    }

  }
  return null
}


/**
 * 将转换失败的线索回退到待处理状态
 */
void revertLeads(String id){
  String objectAPIName = 'LeadsObj'
  Map updateData = [
    "biz_status":"un_processed",
    "transform_time":null
  ]
  def (Boolean error2, Map data2, String errorMessage2) =  Fx.object.update(objectAPIName, id, updateData, UpdateAttribute.builder().triggerWorkflow(false).build())
  if (error2) {
    log.info("线索修改失败:" + errorMessage2 )
  }else{
    log.info("线索修改成功！"  )
  }
}