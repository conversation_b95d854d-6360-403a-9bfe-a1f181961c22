// Perplexity兑换页面内容脚本
(function() {
    'use strict';

    // 防止重复注入
    if (window.perplexityValidatorLoaded) {
        console.log('Content script already loaded, skipping...');
        return;
    }
    window.perplexityValidatorLoaded = true;
    console.log('Perplexity Code Validator content script loaded');

// 等待页面完全加载
let isPageReady = false;
let currentCode = '';
let debugMode = false;
let autoClickMode = true; // 默认开启自动点击
let autoSwitchMode = true; // 默认开启自动切换，检测到错误时自动切换到下一个
let allCodes = [];
let currentCodeIndex = 0;
let nextButton = null;

// 检查页面是否准备就绪
function checkPageReady() {
    // 检查是否在正确的页面
    if (!window.location.href.includes('perplexity.ai/join/p/priority')) {
        console.log('不在正确的页面');
        return false;
    }

    // 检查关键元素是否存在
    const codeInput = document.querySelector('input[placeholder="优惠码"]') ||
                     document.querySelector('input[placeholder*="优惠"]') ||
                     document.querySelector('input[placeholder*="code"]') ||
                     document.querySelector('input.font-mono') ||
                     document.querySelector('form input[type="text"]');

    const submitButton = document.querySelector('button[type="submit"]') ||
                        document.querySelector('form button');

    // 检查页面标识元素
    const perplexityLogo = document.querySelector('img[src*="perplexity"]') ||
                          document.querySelector('img[alt*="perplexity"]') ||
                          document.querySelector('img[src*="priority"]');

    if (codeInput && submitButton) {
        isPageReady = true;
        console.log('页面准备就绪 - 找到输入框和提交按钮');
        console.log('输入框:', codeInput);
        console.log('提交按钮:', submitButton);
        return true;
    }

    console.log('页面未准备就绪 - 缺少关键元素');
    return false;
}

// 等待页面元素加载
function waitForPageElements() {
    return new Promise((resolve) => {
        if (checkPageReady()) {
            resolve();
            return;
        }
        
        const observer = new MutationObserver(() => {
            if (checkPageReady()) {
                observer.disconnect();
                resolve();
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // 超时处理
        setTimeout(() => {
            observer.disconnect();
            resolve();
        }, 10000);
    });
}

// 查找兑换码输入框
function findCodeInput() {
    // 根据实际页面结构，优先查找特定的输入框
    const selectors = [
        'input[placeholder="优惠码"]',
        'input[placeholder*="优惠"]',
        'input[placeholder*="code"]',
        'input[placeholder*="Code"]',
        'input[placeholder*="推广"]',
        'input[placeholder*="邀请"]',
        'input[placeholder*="invite"]',
        'input.font-mono', // 根据HTML中的class
        'form input[type="text"]', // 表单中的文本输入框
        'input[autocomplete="off"]' // 自动完成关闭的输入框
    ];

    for (const selector of selectors) {
        const input = document.querySelector(selector);
        if (input && input.offsetParent !== null && !input.disabled) {
            console.log('找到输入框:', selector, input);
            return input;
        }
    }

    // 如果没找到，尝试查找所有可见的文本输入框
    const allInputs = document.querySelectorAll('input[type="text"], input:not([type])');
    for (const input of allInputs) {
        if (input.offsetParent !== null && !input.disabled && !input.readOnly) {
            console.log('找到备用输入框:', input);
            return input;
        }
    }

    return null;
}

// 查找提交按钮
function findSubmitButton() {
    const selectors = [
        'button[type="submit"]',
        'form button', // 表单中的按钮
        'button:contains("继续")',
        'button:contains("Join")',
        'button:contains("Submit")',
        'button:contains("Redeem")',
        'button:contains("兑换")',
        'button:contains("提交")',
        'input[type="submit"]'
    ];

    for (const selector of selectors) {
        const button = document.querySelector(selector);
        if (button && button.offsetParent !== null && !button.disabled) {
            console.log('找到提交按钮:', selector, button);
            return button;
        }
    }

    // 查找包含特定文本的按钮
    const buttons = document.querySelectorAll('button');
    for (const button of buttons) {
        const text = button.textContent.toLowerCase().trim();
        if ((text.includes('继续') || text.includes('join') || text.includes('submit') ||
             text.includes('redeem') || text.includes('兑换') || text.includes('提交') ||
             text.includes('activate') || text.includes('apply')) &&
            button.offsetParent !== null && !button.disabled) {
            console.log('找到文本匹配按钮:', text, button);
            return button;
        }
    }

    return null;
}

// 填充兑换码
async function fillCode(code) {
    console.log('尝试填充兑换码:', code);

    // 防止重复执行
    if (window.fillCodeInProgress) {
        console.log('填充正在进行中，跳过重复调用');
        return false;
    }
    window.fillCodeInProgress = true;

    try {
        // 重置验证检测状态
        if (window.resetValidationDetection) {
            window.resetValidationDetection();
        }

        const codeInput = findCodeInput();
        if (!codeInput) {
            console.error('未找到兑换码输入框');
            showDebugInfo('未找到兑换码输入框', 'error');
            return false;
        }

        console.log('找到输入框，当前值:', codeInput.value);
        showDebugInfo(`找到输入框: ${codeInput.placeholder || codeInput.className}`);

        // 强制清空输入框
        codeInput.focus();
        codeInput.select();

        // 使用多种方法彻底清空
        codeInput.value = '';
        codeInput.setAttribute('value', '');

        // 触发清空事件
        codeInput.dispatchEvent(new Event('input', { bubbles: true }));
        codeInput.dispatchEvent(new Event('change', { bubbles: true }));

        // 等待一小段时间确保清空完成
        await new Promise(resolve => setTimeout(resolve, 100));

        // 再次确认已清空
        if (codeInput.value !== '') {
            console.log('输入框未完全清空，强制清空');
            codeInput.value = '';
            // 使用execCommand作为备选方案
            codeInput.select();
            document.execCommand('delete');
        }

        // 直接设置新值
        codeInput.value = code;

        // 触发所有必要的事件
        codeInput.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
        codeInput.dispatchEvent(new Event('change', { bubbles: true }));
        codeInput.dispatchEvent(new Event('keyup', { bubbles: true }));

        currentCode = code;
        console.log('兑换码已填充:', code, '最终值:', codeInput.value);

        // 验证填充结果
        if (codeInput.value !== code) {
            console.error('填充失败，期望:', code, '实际:', codeInput.value);
            showDebugInfo(`填充失败，期望: ${code}, 实际: ${codeInput.value}`, 'error');
            return false;
        }

        // 添加视觉反馈
        const originalBg = codeInput.style.backgroundColor;
        codeInput.style.backgroundColor = '#e3f2fd';
        codeInput.style.transition = 'background-color 0.3s';

        setTimeout(() => {
            codeInput.style.backgroundColor = originalBg;
        }, 1500);

        showDebugInfo(`成功填充兑换码: ${code}`, 'success');
        return true;

    } finally {
        // 确保释放锁
        setTimeout(() => {
            window.fillCodeInProgress = false;
        }, 500);
    }
}

// 创建"下一个"按钮
function createNextButton() {
    if (nextButton) {
        return; // 按钮已存在
    }

    const submitButton = findSubmitButton();
    if (!submitButton) {
        showDebugInfo('未找到提交按钮，无法创建下一个按钮', 'error');
        return;
    }

    // 创建下一个按钮
    nextButton = document.createElement('button');
    nextButton.type = 'button';
    nextButton.textContent = '下一个';
    nextButton.id = 'pplx-next-code-btn';

    // 复制提交按钮的样式
    nextButton.className = submitButton.className;
    nextButton.style.cssText = `
        ${submitButton.style.cssText}
        margin-left: 10px;
        background: #6c757d !important;
        opacity: 0.7;
        transition: all 0.2s ease;
    `;

    // 添加悬停效果
    nextButton.addEventListener('mouseenter', () => {
        nextButton.style.opacity = '1';
        nextButton.style.transform = 'scale(1.02)';
    });

    nextButton.addEventListener('mouseleave', () => {
        nextButton.style.opacity = '0.7';
        nextButton.style.transform = 'scale(1)';
    });

    // 添加点击事件
    nextButton.addEventListener('click', async () => {
        await handleNextCode();
    });

    // 将按钮插入到提交按钮旁边
    const buttonContainer = submitButton.parentElement;
    if (buttonContainer) {
        // 如果容器是flex布局，保持布局
        if (window.getComputedStyle(buttonContainer).display === 'flex') {
            buttonContainer.style.gap = '10px';
        }
        buttonContainer.appendChild(nextButton);
        showDebugInfo('已创建下一个按钮', 'success');
    } else {
        // 如果没有容器，在提交按钮后插入
        submitButton.insertAdjacentElement('afterend', nextButton);
        showDebugInfo('已在提交按钮后插入下一个按钮', 'success');
    }

    updateNextButtonState();
}

// 处理下一个兑换码
async function handleNextCode() {
    // 防止重复执行
    if (window.handleNextCodeInProgress) {
        console.log('handleNextCode正在执行中，跳过重复调用');
        return;
    }
    window.handleNextCodeInProgress = true;

    try {
        if (!allCodes || allCodes.length === 0) {
            showDebugInfo('没有可用的兑换码', 'error');
            return;
        }

        // 移除当前兑换码（标记为无效）
        if (currentCodeIndex < allCodes.length) {
            const removedCode = allCodes.splice(currentCodeIndex, 1)[0];
            showDebugInfo(`移除无效兑换码: ${removedCode}`, 'info');

            // 通知popup更新兑换码列表
            chrome.runtime.sendMessage({
                action: 'updateCodes',
                codes: allCodes,
                currentIndex: currentCodeIndex
            });
        }

        // 如果还有兑换码，填充下一个
        if (allCodes.length > 0) {
            // 调整索引
            if (currentCodeIndex >= allCodes.length) {
                currentCodeIndex = 0;
            }

            const nextCode = allCodes[currentCodeIndex];
            showDebugInfo(`填充下一个兑换码: ${nextCode}`, 'info');

            // 重置检测状态
            if (window.resetValidationDetection) {
                window.resetValidationDetection();
            }

            // 等待一段时间确保页面稳定
            await new Promise(resolve => setTimeout(resolve, 500));

            // 填充新的兑换码
            const fillSuccess = await fillCode(nextCode);

            if (fillSuccess) {
                // 根据设置决定是否自动点击继续按钮
                if (autoClickMode) {
                    setTimeout(async () => {
                        await autoClickContinueButton();
                    }, 1000); // 增加等待时间确保填充完成
                }

                updateNextButtonState();
            } else {
                showDebugInfo('填充兑换码失败，停止自动切换', 'error');
            }
        } else {
            showDebugInfo('所有兑换码已用完', 'success');
            removeNextButton();
        }
    } finally {
        // 释放锁
        setTimeout(() => {
            window.handleNextCodeInProgress = false;
        }, 1000);
    }
}

// 更新下一个按钮状态
function updateNextButtonState() {
    if (!nextButton) return;

    const hasMoreCodes = allCodes && allCodes.length > 1;
    nextButton.disabled = !hasMoreCodes;

    if (hasMoreCodes) {
        nextButton.style.opacity = '0.7';
        nextButton.style.cursor = 'pointer';
        nextButton.title = `还有 ${allCodes.length - 1} 个兑换码`;
    } else {
        nextButton.style.opacity = '0.3';
        nextButton.style.cursor = 'not-allowed';
        nextButton.title = '没有更多兑换码';
    }
}

// 移除下一个按钮
function removeNextButton() {
    if (nextButton && nextButton.parentNode) {
        nextButton.parentNode.removeChild(nextButton);
        nextButton = null;
        showDebugInfo('已移除下一个按钮', 'info');
    }
}

// 自动点击继续按钮
async function autoClickContinueButton() {
    try {
        const submitButton = findSubmitButton();
        if (!submitButton) {
            showDebugInfo('未找到继续按钮，无法自动点击', 'error');
            return false;
        }

        // 检查按钮是否可点击
        if (submitButton.disabled) {
            showDebugInfo('继续按钮被禁用，等待启用...', 'info');

            // 等待按钮启用，最多等待3秒
            let attempts = 0;
            while (submitButton.disabled && attempts < 30) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }

            if (submitButton.disabled) {
                showDebugInfo('继续按钮仍被禁用，无法自动点击', 'error');
                return false;
            }
        }

        showDebugInfo('自动点击继续按钮', 'info');

        // 在点击前设置一个监听器来检测提交后的结果
        setupSubmitResultListener();

        // 模拟真实的用户点击
        const clickEvent = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window,
            button: 0,
            buttons: 1,
            clientX: submitButton.getBoundingClientRect().left + submitButton.offsetWidth / 2,
            clientY: submitButton.getBoundingClientRect().top + submitButton.offsetHeight / 2
        });

        // 添加视觉反馈
        submitButton.style.transform = 'scale(0.95)';
        setTimeout(() => {
            submitButton.style.transform = '';
        }, 150);

        // 触发点击事件
        submitButton.dispatchEvent(clickEvent);

        // 如果上面的方法不起作用，尝试直接调用click()
        setTimeout(() => {
            if (submitButton.click) {
                submitButton.click();
            }
        }, 100);

        showDebugInfo('已自动点击继续按钮', 'success');
        return true;

    } catch (error) {
        console.error('自动点击继续按钮失败:', error);
        showDebugInfo(`自动点击失败: ${error.message}`, 'error');
        return false;
    }
}

// 设置提交结果监听器
function setupSubmitResultListener() {
    // 防止重复设置监听器
    if (window.submitListenerActive) {
        console.log('提交监听器已激活，跳过重复设置');
        return;
    }
    window.submitListenerActive = true;

    let checkCount = 0;
    const maxChecks = 8; // 减少检查次数

    const checkResult = () => {
        checkCount++;

        // 检查是否有错误消息出现
        const errorSelectors = [
            '.text-superAlt',
            '[class*="error"]',
            'div[class*="superAlt"]'
        ];

        for (const selector of errorSelectors) {
            const elements = document.querySelectorAll(selector);
            for (const element of elements) {
                const text = element.textContent.trim();
                if (text &&
                    (text.includes('无效的促销代码') || text.includes('请尝试复制并粘贴代码')) &&
                    element.offsetParent !== null &&
                    text.length < 100 && // 更严格的长度限制
                    text.length > 10) { // 确保不是空文本

                    showDebugInfo(`提交后检测到错误: ${text}`, 'error');
                    console.log('提交后检测到错误:', text);

                    // 清除监听器状态
                    window.submitListenerActive = false;

                    // 如果开启了自动切换，自动切换到下一个
                    if (autoSwitchMode && allCodes && allCodes.length > 1) {
                        showDebugInfo('自动切换到下一个兑换码', 'info');
                        setTimeout(async () => {
                            await handleNextCode();
                            showAutoNextNotification();
                        }, 1500);
                    }
                    return; // 找到错误，停止检查
                }
            }
        }

        // 如果还没有检查完最大次数，继续检查
        if (checkCount < maxChecks) {
            setTimeout(checkResult, 750); // 增加检查间隔
        } else {
            // 检查完成，清除监听器状态
            window.submitListenerActive = false;
            showDebugInfo('提交结果检查完成，未发现错误', 'info');
        }
    };

    // 延迟开始检查，等待页面响应
    setTimeout(checkResult, 1500);
}

// 显示自动切换下一个的提示
function showAutoNextNotification() {
    // 创建提示元素
    const notification = document.createElement('div');
    notification.id = 'auto-next-notification';
    notification.innerHTML = `
        <div style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 10001;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            font-weight: 500;
            max-width: 300px;
            animation: slideInRight 0.3s ease-out;
        ">
            <div style="display: flex; align-items: center; gap: 10px;">
                <div style="
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.2);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 12px;
                ">⚡</div>
                <div>
                    <div style="font-weight: 600; margin-bottom: 2px;">兑换码无效</div>
                    <div style="font-size: 12px; opacity: 0.9;">已自动切换到下一个兑换码</div>
                </div>
            </div>
        </div>
    `;

    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    `;

    if (!document.getElementById('auto-next-styles')) {
        style.id = 'auto-next-styles';
        document.head.appendChild(style);
    }

    // 移除现有的通知
    const existingNotification = document.getElementById('auto-next-notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }
    }, 3000);

    showDebugInfo('显示自动切换提示', 'info');
}

// 重置所有状态
function resetAllStates() {
    window.fillCodeInProgress = false;
    window.handleNextCodeInProgress = false;
    window.submitListenerActive = false;

    // 重置验证检测状态
    if (typeof hasDetectedResult !== 'undefined') {
        hasDetectedResult = false;
    }

    showDebugInfo('已重置所有状态', 'info');
}

// 暴露重置函数到全局
window.resetValidationDetection = resetAllStates;

// 监听页面变化，检测验证结果
function setupValidationListener() {
    let hasDetectedResult = false; // 防止重复检测
    let lastDetectionTime = 0;
    const DETECTION_COOLDOWN = 2000; // 2秒冷却时间

    const observer = new MutationObserver((mutations) => {
        // 防止过于频繁的检测
        const now = Date.now();
        if (hasDetectedResult || (now - lastDetectionTime) < DETECTION_COOLDOWN) {
            return;
        }

        // 只有在有兑换码填充后才开始检测
        if (!currentCode) {
            return;
        }

        for (const mutation of mutations) {
            // 检查文本内容变化
            if (mutation.type === 'childList') {
                // 检查新添加的节点中是否包含错误消息
                for (const node of mutation.addedNodes) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const text = node.textContent || '';
                        if (text.includes('无效的促销代码') || text.includes('请尝试复制并粘贴代码')) {
                            showDebugInfo(`检测到新的错误消息: ${text}`, 'error');
                            console.log('检测到新的错误消息:', text, 'node:', node);

                            hasDetectedResult = true;

                            if (autoSwitchMode && allCodes && allCodes.length > 1) {
                                showDebugInfo('自动切换到下一个兑换码', 'info');
                                setTimeout(async () => {
                                    await handleNextCode();
                                    showAutoNextNotification();
                                }, 1500);
                            } else {
                                chrome.runtime.sendMessage({
                                    action: 'codeValidated',
                                    success: false,
                                    code: currentCode,
                                    error: text
                                });
                            }
                            return;
                        }
                    }
                }
            }

            // 检查是否显示了成功状态
            if (mutation.type === 'childList' || mutation.type === 'attributes') {
                // 检查成功图标是否出现且可见
                const successIcon = document.querySelector('svg[data-icon="circle-check"]');
                if (successIcon && successIcon.offsetParent !== null) {
                    // 检查成功图标的父容器的opacity
                    let parentContainer = successIcon.closest('div');
                    while (parentContainer && !parentContainer.classList.contains('absolute')) {
                        parentContainer = parentContainer.parentElement;
                    }

                    if (parentContainer) {
                        const computedStyle = window.getComputedStyle(parentContainer);
                        const opacity = parseFloat(computedStyle.opacity);

                        // 只有当包含成功图标的绝对定位容器opacity > 0.8时才认为是真正的成功
                        if (opacity > 0.8) {
                            showDebugInfo(`检测到成功图标，容器opacity: ${opacity}`, 'success');
                            console.log('检测到成功状态:', successIcon, 'container opacity:', opacity);

                            hasDetectedResult = true;
                            chrome.runtime.sendMessage({
                                action: 'codeValidated',
                                success: true,
                                code: currentCode
                            });
                            return;
                        } else {
                            showDebugInfo(`成功图标存在但不可见，opacity: ${opacity}`, 'info');
                        }
                    }
                }

                // 检查成功文本 - 更精确的检测，只检查真正的成功状态
                // 成功状态应该是在一个特定的覆盖层中显示
                const successOverlay = document.querySelector('div.absolute.inset-0');
                if (successOverlay) {
                    const computedStyle = window.getComputedStyle(successOverlay);
                    const opacity = parseFloat(computedStyle.opacity);

                    // 只有当覆盖层完全可见时才检查成功文本
                    if (opacity > 0.9) {
                        // 使用XPath查找包含成功文本的元素
                        const xpath = ".//div[contains(text(), '优惠码已应用')]";
                        const result = document.evaluate(xpath, successOverlay, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                        const successText = result.singleNodeValue;

                        if (successText) {
                            showDebugInfo('检测到成功覆盖层，验证成功', 'success');
                            console.log('检测到成功状态，覆盖层opacity:', opacity);

                            hasDetectedResult = true;
                            chrome.runtime.sendMessage({
                                action: 'codeValidated',
                                success: true,
                                code: currentCode
                            });
                            return;
                        }
                    } else {
                        showDebugInfo(`成功覆盖层存在但不可见，opacity: ${opacity}`, 'info');
                    }
                }

                // 检查错误消息 - 根据实际HTML结构检测
                const errorSelectors = [
                    '.text-superAlt',
                    '[class*="text-red"]',
                    '[class*="error"]',
                    '.font-sans.text-xs.font-medium.text-superAlt', // 更精确的选择器
                    'div[class*="superAlt"]'
                ];

                for (const selector of errorSelectors) {
                    const errorElements = document.querySelectorAll(selector);
                    for (const element of errorElements) {
                        const errorText = element.textContent.trim();

                        // 检查是否包含错误关键词且元素可见
                        if (errorText &&
                            (errorText.includes('无效的促销代码') ||
                             errorText.includes('该代码已被使用') || // 新增的判断条件
                             errorText.includes('无效') ||
                             errorText.includes('错误') ||
                             errorText.includes('失败') ||
                             errorText.includes('invalid') ||
                             errorText.includes('请尝试复制并粘贴代码') ||
                             errorText.includes('拼写错误')) &&
                            element.offsetParent !== null &&
                            errorText.length > 5) { // 确保不是空的或很短的文本

                            showDebugInfo(`检测到错误消息: ${errorText}`, 'error');
                            console.log('检测到错误消息:', errorText, 'element:', element);

                            hasDetectedResult = true;

                            // 如果开启了自动切换模式，自动切换到下一个兑换码
                            if (autoSwitchMode && allCodes && allCodes.length > 1) {
                                showDebugInfo('自动切换到下一个兑换码', 'info');
                                setTimeout(async () => {
                                    await handleNextCode();
                                    showAutoNextNotification();
                                }, 1500); // 等待1.5秒后自动切换
                            } else {
                                // 发送验证失败消息
                                chrome.runtime.sendMessage({
                                    action: 'codeValidated',
                                    success: false,
                                    code: currentCode,
                                    error: errorText
                                });
                            }
                            return;
                        }
                    }
                }
            }
        }

        lastDetectionTime = now;
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'style', 'opacity']
    });

    // 重置检测状态的函数
    window.resetValidationDetection = () => {
        hasDetectedResult = false;
        lastDetectionTime = 0;
        showDebugInfo('重置验证检测状态', 'info');
    };

    // 监听URL变化（可能表示成功跳转）
    let currentUrl = window.location.href;
    const urlCheckInterval = setInterval(() => {
        if (window.location.href !== currentUrl) {
            currentUrl = window.location.href;
            if (!currentUrl.includes('/join/p/priority')) {
                showDebugInfo('页面跳转，验证可能成功', 'success');
                console.log('页面已跳转，可能验证成功');

                if (!hasDetectedResult) {
                    chrome.runtime.sendMessage({
                        action: 'codeValidated',
                        success: true,
                        code: currentCode
                    });
                }
                clearInterval(urlCheckInterval);
            }
        }
    }, 1000);

    // 5分钟后清理定时器
    setTimeout(() => {
        clearInterval(urlCheckInterval);
    }, 300000);

    // 添加定期检查错误消息的机制
    const errorCheckInterval = setInterval(() => {
        if (!currentCode || hasDetectedResult) {
            return;
        }

        // 只检查特定的错误元素，避免检查整个页面
        const errorSelectors = [
            '.text-superAlt',
            '[class*="error"]',
            'div[class*="superAlt"]'
        ];

        for (const selector of errorSelectors) {
            const errorElements = document.querySelectorAll(selector);
            for (const element of errorElements) {
                const text = element.textContent.trim();
                if (text &&
                    (text.includes('无效的促销代码') || text.includes('请尝试复制并粘贴代码')) &&
                    element.offsetParent !== null &&
                    text.length < 100 && // 更严格的长度限制
                    text.length > 10 && // 确保不是空文本
                    !text.includes('服务条款') && // 排除页面底部文本
                    !text.includes('隐私政策')) { // 排除页面底部文本

                    showDebugInfo(`定期检查发现错误: ${text}`, 'error');
                    console.log('定期检查发现错误:', text);

                    hasDetectedResult = true;
                    clearInterval(errorCheckInterval);

                    if (autoSwitchMode && allCodes && allCodes.length > 1) {
                        showDebugInfo('自动切换到下一个兑换码', 'info');
                        setTimeout(async () => {
                            await handleNextCode();
                            showAutoNextNotification();
                        }, 1500);
                    } else {
                        // 不自动切换时，只发送验证失败消息
                        showDebugInfo('检测到错误，但自动切换已关闭', 'info');
                        chrome.runtime.sendMessage({
                            action: 'codeValidated',
                            success: false,
                            code: currentCode,
                            error: text
                        });
                    }
                    return;
                }
            }
        }
    }, 2000); // 每2秒检查一次，减少频率

    // 5分钟后清理错误检查定时器
    setTimeout(() => {
        clearInterval(errorCheckInterval);
    }, 300000);
}

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('收到消息:', message);
    showDebugInfo(`收到消息: ${message.action}`);

    if (message.action === 'fillCode') {
        showDebugInfo(`开始填充兑换码: ${message.code}`);

        // 如果是第一次填充，保存所有兑换码
        if (message.allCodes) {
            allCodes = [...message.allCodes];
            currentCodeIndex = message.currentIndex || 0;
            showDebugInfo(`接收到 ${allCodes.length} 个兑换码`, 'info');

            // 创建下一个按钮
            setTimeout(() => {
                createNextButton();
            }, 1000); // 延迟1秒确保页面元素稳定
        }

        fillCode(message.code).then(async (success) => {
            if (success) {
                showDebugInfo('兑换码填充成功', 'success');

                // 如果需要自动点击继续按钮
                if (message.autoClick) {
                    setTimeout(async () => {
                        await autoClickContinueButton();
                    }, 800);
                }
            } else {
                showDebugInfo('兑换码填充失败', 'error');
            }
            sendResponse({ success });
        }).catch(error => {
            console.error('填充兑换码时出错:', error);
            showDebugInfo(`填充错误: ${error.message}`, 'error');
            sendResponse({ success: false, error: error.message });
        });
        return true; // 保持消息通道开放
    }

    if (message.action === 'setDebugMode') {
        debugMode = message.enabled;
        console.log('调试模式已', debugMode ? '开启' : '关闭');
        if (debugMode) {
            showDebugInfo('调试模式已开启', 'success');
        } else {
            // 移除调试面板
            const debugDiv = document.getElementById('pplx-validator-debug');
            if (debugDiv) {
                debugDiv.remove();
            }
        }
        sendResponse({ success: true });
        return true;
    }

    if (message.action === 'resetDetection') {
        if (window.resetValidationDetection) {
            window.resetValidationDetection();
            showDebugInfo('手动重置验证检测状态', 'info');
        }
        sendResponse({ success: true });
        return true;
    }

    if (message.action === 'checkCode') {
        checkCodeViaAPI(message.code).then(result => {
            sendResponse(result);
        }).catch(error => {
            console.error('API检测失败:', error);
            sendResponse({ isValid: false, error: error.message });
        });
        return true; // 保持消息通道开放
    }

    if (message.action === 'setAutoClickMode') {
        autoClickMode = message.enabled;
        console.log('自动点击模式已', autoClickMode ? '开启' : '关闭');
        showDebugInfo(`自动点击模式已${autoClickMode ? '开启' : '关闭'}`, 'info');
        sendResponse({ success: true });
        return true;
    }

    if (message.action === 'setAutoSwitchMode') {
        autoSwitchMode = message.enabled;
        console.log('自动切换模式已', autoSwitchMode ? '开启' : '关闭');
        showDebugInfo(`自动切换模式已${autoSwitchMode ? '开启' : '关闭'}`, 'info');
        sendResponse({ success: true });
        return true;
    }
});

// 添加调试信息显示
function showDebugInfo(message, type = 'info') {
    if (!debugMode) return; // 只在调试模式下显示

    const debugDiv = document.getElementById('pplx-validator-debug') || (() => {
        const div = document.createElement('div');
        div.id = 'pplx-validator-debug';
        div.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            z-index: 10000;
            max-width: 350px;
            max-height: 400px;
            overflow-y: auto;
            word-wrap: break-word;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            border: 1px solid #333;
        `;

        // 添加关闭按钮
        const closeBtn = document.createElement('div');
        closeBtn.innerHTML = '×';
        closeBtn.style.cssText = `
            position: absolute;
            top: 5px;
            right: 8px;
            cursor: pointer;
            font-size: 16px;
            color: #ccc;
            hover: #fff;
        `;
        closeBtn.onclick = () => {
            debugMode = false;
            div.remove();
        };
        div.appendChild(closeBtn);

        // 添加标题
        const title = document.createElement('div');
        title.innerHTML = 'Perplexity Validator Debug';
        title.style.cssText = `
            font-weight: bold;
            margin-bottom: 8px;
            padding-bottom: 5px;
            border-bottom: 1px solid #444;
            color: #74c0fc;
        `;
        div.appendChild(title);

        document.body.appendChild(div);
        return div;
    })();

    const timestamp = new Date().toLocaleTimeString();
    const color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : '#74c0fc';

    const messageDiv = document.createElement('div');
    messageDiv.style.cssText = `color: ${color}; margin: 2px 0; font-size: 11px;`;
    messageDiv.innerHTML = `[${timestamp}] ${message}`;

    debugDiv.appendChild(messageDiv);
    debugDiv.scrollTop = debugDiv.scrollHeight;

    // 保持最多30条消息
    const messages = debugDiv.children;
    if (messages.length > 32) { // 30条消息 + 标题 + 关闭按钮
        debugDiv.removeChild(messages[2]); // 移除第一条消息（跳过标题和关闭按钮）
    }
}

// 加载调试模式设置
async function loadDebugMode() {
    try {
        if (typeof chrome !== 'undefined' && chrome.storage) {
            const result = await chrome.storage.local.get(['debugMode']);
            debugMode = result.debugMode || false;
        }
    } catch (error) {
        console.log('无法加载调试模式设置:', error);
    }
}

// 加载自动点击模式设置
async function loadAutoClickMode() {
    try {
        if (typeof chrome !== 'undefined' && chrome.storage) {
            const result = await chrome.storage.local.get(['autoClickMode']);
            autoClickMode = result.autoClickMode !== false; // 默认为true
        }
    } catch (error) {
        console.log('无法加载自动点击模式设置:', error);
    }
}

// 加载自动切换模式设置
async function loadAutoSwitchMode() {
    try {
        if (typeof chrome !== 'undefined' && chrome.storage) {
            const result = await chrome.storage.local.get(['autoSwitchMode']);
            autoSwitchMode = result.autoSwitchMode !== false; // 默认为true，除非用户主动关闭
        }
    } catch (error) {
        console.log('无法加载自动切换模式设置:', error);
    }
}

// 通过API检测兑换码
async function checkCodeViaAPI(code) {
    try {
        showDebugInfo(`开始API检测兑换码: ${code}`, 'info');

        // 构建API URL
        const apiUrl = `https://www.perplexity.ai/rest/billing/get-coupon-metadata?discount_code=${encodeURIComponent(code)}&version=2.18&source=default`;

        // 发起API请求
        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'accept': '*/*',
                'accept-language': 'zh-CN,zh;q=0.9',
                'cache-control': 'no-cache',
                'pragma': 'no-cache',
                'priority': 'u=1, i',
                'referer': 'https://www.perplexity.ai/join/p/priority',
                'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                'sec-ch-ua-arch': '"arm"',
                'sec-ch-ua-bitness': '"64"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"macOS"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': navigator.userAgent,
                'x-app-apiclient': 'default',
                'x-app-apiversion': '2.18',
                'x-perplexity-request-endpoint': apiUrl,
                'x-perplexity-request-reason': 'partner-signup',
                'x-perplexity-request-try-number': '1'
            },
            credentials: 'include' // 包含cookies
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        showDebugInfo(`API响应: ${JSON.stringify(data)}`, 'info');

        // 判断兑换码是否有效
        const isValid = data.status === 'success' && !data.error_code;

        if (isValid) {
            showDebugInfo(`兑换码 ${code} 有效`, 'success');
        } else {
            const errorCode = data.error_code || 'UNKNOWN_ERROR';
            showDebugInfo(`兑换码 ${code} 无效: ${errorCode}`, 'error');
        }

        return {
            isValid: isValid,
            data: data,
            code: code
        };

    } catch (error) {
        console.error(`检测兑换码 ${code} 时出错:`, error);
        showDebugInfo(`检测兑换码 ${code} 失败: ${error.message}`, 'error');

        return {
            isValid: false,
            error: error.message,
            code: code
        };
    }
}

// 初始化
(async function init() {
    // 防止重复初始化
    if (window.perplexityValidatorInitialized) {
        console.log('Content script already initialized, skipping...');
        return;
    }
    window.perplexityValidatorInitialized = true;

    console.log('初始化content script');

    // 加载设置
    await loadDebugMode();
    await loadAutoClickMode();
    await loadAutoSwitchMode();

    showDebugInfo('Perplexity Code Validator 已加载');
    showDebugInfo(`自动点击模式: ${autoClickMode ? '开启' : '关闭'}`, 'info');
    showDebugInfo(`自动切换模式: ${autoSwitchMode ? '开启' : '关闭'}`, 'info');

    await waitForPageElements();

    if (isPageReady) {
        setupValidationListener();
        showDebugInfo('页面准备完成，监听器已设置', 'success');
        console.log('Content script初始化完成');
    } else {
        showDebugInfo('页面未准备就绪，请刷新页面', 'error');
        console.log('页面未准备就绪');
    }
})();

})(); // 闭合IIFE
