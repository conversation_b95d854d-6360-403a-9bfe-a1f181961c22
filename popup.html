<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Perplexity Code Validator</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Perplexity 兑换码验证器</h1>
            <button id="helpBtn" class="help-btn" title="显示快捷键">?</button>
        </div>
        
        <div class="status-section">
            <div class="status-item">
                <span class="label">当前状态:</span>
                <span id="currentStatus" class="status">待机中</span>
            </div>
            <div class="status-item">
                <span class="label">剩余兑换码:</span>
                <span id="remainingCodes" class="count">0</span>
            </div>
        </div>

        <div class="input-section">
            <label for="codesInput">兑换码列表 (每行一个):</label>
            <textarea 
                id="codesInput" 
                placeholder="请输入兑换码，每行一个&#10;例如：&#10;ABC123DEF&#10;XYZ789GHI&#10;..."
                rows="8"
            ></textarea>
        </div>

        <div class="controls">
            <button id="saveCodesBtn" class="btn btn-primary" title="保存兑换码 (Ctrl+S)">保存兑换码</button>
            <button id="startValidationBtn" class="btn btn-success" disabled title="开始验证 (Ctrl+Enter)">开始验证</button>
            <button id="batchCheckBtn" class="btn btn-info" disabled title="批量检测兑换码">批量检测</button>
            <button id="nextCodeBtn" class="btn btn-secondary" disabled title="下一个兑换码 (Ctrl+N)">下一个兑换码</button>
            <button id="clearAllBtn" class="btn btn-danger" title="清空所有兑换码">清空所有</button>
        </div>

        <div class="current-code-section">
            <label>当前兑换码:</label>
            <div id="currentCode" class="current-code">无</div>
        </div>

        <div class="batch-check-section">
            <div class="batch-settings">
                <label for="batchSize">并发数量:</label>
                <select id="batchSize">
                    <option value="5">5个</option>
                    <option value="10" selected>10个</option>
                    <option value="15">15个</option>
                    <option value="20">20个</option>
                </select>
            </div>
            <div id="batchProgress" class="batch-progress" style="display: none;">
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill"></div>
                </div>
                <div id="progressText" class="progress-text">检测中...</div>
            </div>
            <div id="batchResults" class="batch-results" style="display: none;">
                <div class="results-summary">
                    <span id="validCount" class="valid-count">有效: 0</span>
                    <span id="invalidCount" class="invalid-count">无效: 0</span>
                </div>
            </div>
        </div>

        <div class="instructions">
            <h3>使用说明:</h3>
            <ol>
                <li>在上方文本框中输入所有兑换码（每行一个）</li>
                <li>点击"保存兑换码"</li>
                <li><strong>新功能：点击"批量检测"快速筛选有效兑换码</strong></li>
                <li>或者打开 <a href="https://www.perplexity.ai/join/p/priority" target="_blank">Perplexity兑换页面</a></li>
                <li>点击"开始验证"自动填充第一个兑换码</li>
                <li><strong>页面上会出现"下一个"按钮</strong>，无效时直接点击即可</li>
                <li>有效的兑换码会自动保留，无效的会被自动移除</li>
            </ol>

            <div class="debug-section">
                <label>
                    <input type="checkbox" id="debugMode"> 显示调试信息
                </label>
                <label style="margin-top: 8px;">
                    <input type="checkbox" id="autoClickMode" checked> 自动点击继续按钮
                </label>
                <label style="margin-top: 8px;">
                    <input type="checkbox" id="autoSwitchMode"> 无效时自动切换下一个
                </label>
                <div style="margin-top: 8px; font-size: 12px; color: #666; padding-left: 20px;">
                    关闭此选项可以手动一个个验证兑换码
                </div>
                <button id="resetDetectionBtn" class="btn btn-secondary" style="margin-top: 8px; font-size: 11px; padding: 6px 12px;">重置检测状态</button>
            </div>
        </div>
    </div>
    <script src="popup.js"></script>
</body>
</html>
